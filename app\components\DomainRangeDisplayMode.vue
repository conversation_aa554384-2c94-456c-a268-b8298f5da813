<template>
  <div class="max-w-4xl mx-auto">
    <!-- Display Card -->
    <UCard class="mb-6">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
            Domain & Range Practice
          </h2>
          <UBadge color="green" variant="soft" size="lg">
            Display Mode
          </UBadge>
        </div>
      </template>

      <div class="space-y-8">
        <!-- Question Type -->
        <div class="text-center">
          <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
            Task
          </div>
          <UBadge 
            :color="getQuestionColor(currentProblem?.questionType)" 
            variant="soft" 
            size="lg"
            class="text-lg px-4 py-2"
          >
            {{ currentProblem?.description }}
          </UBadge>
        </div>

        <!-- Function Display -->
        <div class="text-center">
          <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-4">
            Function
          </div>
          <div class="p-8 bg-green-50 dark:bg-green-900/20 rounded-xl border-2 border-green-200 dark:border-green-700">
            <div class="text-green-900 dark:text-green-100 font-mono text-4xl font-semibold leading-relaxed">
              {{ currentProblem?.equation }}
            </div>
          </div>
        </div>

        <!-- Function Type Info -->
        <div class="text-center" v-if="currentProblem">
          <UBadge 
            :color="getFunctionTypeColor(currentProblem.functionType)" 
            variant="outline"
            size="lg"
          >
            {{ getFunctionTypeName(currentProblem.functionType) }}
          </UBadge>
        </div>

        <!-- Instructions -->
        <div class="text-center">
          <p class="text-gray-600 dark:text-gray-400 text-lg">
            This function is displayed for teacher-led assessment and discussion.
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between items-center">
          <UButton 
            @click="$emit('backToGrid')" 
            variant="outline"
            size="lg"
            icon="i-lucide-grid-3x3"
          >
            Back to Grid
          </UButton>
          
          <UButton
            @click="generateNewProblem"
            size="lg"
            icon="i-lucide-refresh-cw"
            :loading="generating"
          >
            Generate Another
          </UButton>
        </div>
      </template>
    </UCard>

    <!-- Problem History (Optional) -->
    <UCard v-if="problemHistory.length > 1">
      <template #header>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Recent Problems
        </h3>
      </template>
      
      <div class="space-y-3">
        <div 
          v-for="(problem, index) in problemHistory.slice(-5).reverse()" 
          :key="problem.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UBadge 
              :color="getQuestionColor(problem.questionType)" 
              variant="soft"
              size="sm"
            >
              {{ problem.description }}
            </UBadge>
            <span class="font-mono text-sm text-gray-700 dark:text-gray-300">
              {{ problem.equation }}
            </span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ index === 0 ? 'Current' : `${index + 1} ago` }}
          </span>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { DisplayDomainRangeProblem, FunctionType } from '~/types/game'
import { generateDisplayDomainRangeProblem } from '~/utils/problemGenerator'

interface Emits {
  (e: 'backToGrid'): void
}

defineEmits<Emits>()

// State
const currentProblem = ref<DisplayDomainRangeProblem | null>(null)
const generating = ref(false)
const problemHistory = ref<DisplayDomainRangeProblem[]>([])

// Methods
const generateNewProblem = async () => {
  generating.value = true
  
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const newProblem = generateDisplayDomainRangeProblem()
    
    // Add current problem to history before replacing
    if (currentProblem.value) {
      problemHistory.value.push(currentProblem.value)
    }
    
    currentProblem.value = newProblem
  } finally {
    generating.value = false
  }
}

const getQuestionColor = (questionType?: string) => {
  switch (questionType) {
    case 'domain':
      return 'blue'
    case 'range':
      return 'purple'
    default:
      return 'gray'
  }
}

const getFunctionTypeColor = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'green'
    case 'quadratic':
      return 'blue'
    case 'cubic':
      return 'purple'
    case 'absolute':
      return 'orange'
    case 'square-root':
      return 'red'
    default:
      return 'gray'
  }
}

const getFunctionTypeName = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'Linear Function'
    case 'quadratic':
      return 'Quadratic Function'
    case 'cubic':
      return 'Cubic Function'
    case 'absolute':
      return 'Absolute Value Function'
    case 'square-root':
      return 'Square Root Function'
    default:
      return 'Function'
  }
}

// Initialize with first problem
onMounted(() => {
  generateNewProblem()
})
</script>
