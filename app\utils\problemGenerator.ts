import type {
  NotationProblem,
  DomainRangeProblem,
  NotationType,
  FunctionType,
  ProblemGeneratorOptions,
  DisplayNotationProblem,
  DisplayDomainRangeProblem
} from '~/types/game'

/**
 * Generate random integer within range
 */
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generate random coefficient (avoiding 0)
 */
function randomCoeff(): number {
  const coeff = randomInt(-5, 5)
  return coeff === 0 ? 1 : coeff
}

/**
 * Generate notation conversion problems
 */
export function generateNotationProblem(
  sourceNotation: NotationType,
  targetNotation: NotationType,
  options: ProblemGeneratorOptions = {}
): NotationProblem {
  const id = `notation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  // Generate base inequality
  const variable = 'x'
  const value = randomInt(-10, 10)
  const operators = ['>', '<', '≥', '≤']
  const operator = operators[randomInt(0, operators.length - 1)]

  let problem = ''
  let correctAnswers: string[] = []

  if (sourceNotation === 'algebraic') {
    problem = `${variable} ${operator} ${value}`

    if (targetNotation === 'interval') {
      correctAnswers = convertAlgebraicToInterval(variable, operator, value)
    } else if (targetNotation === 'set-builder') {
      correctAnswers = convertAlgebraicToSetBuilder(variable, operator, value)
    }
  } else if (sourceNotation === 'interval') {
    const intervalNotation = generateIntervalNotation()
    problem = intervalNotation.notation

    if (targetNotation === 'algebraic') {
      correctAnswers = [intervalNotation.algebraic]
    } else if (targetNotation === 'set-builder') {
      correctAnswers = [intervalNotation.setBuilder]
    }
  } else if (sourceNotation === 'set-builder') {
    const setBuilderNotation = generateSetBuilderNotation()
    problem = setBuilderNotation.notation

    if (targetNotation === 'algebraic') {
      correctAnswers = [setBuilderNotation.algebraic]
    } else if (targetNotation === 'interval') {
      correctAnswers = [setBuilderNotation.interval]
    }
  }

  return {
    id,
    type: 'notation-conversion',
    sourceNotation,
    targetNotation,
    problem: `Convert to ${targetNotation.replace('-', ' ')} notation: ${problem}`,
    correctAnswers,
    hint: options.includeHints ? generateNotationHint(targetNotation) : undefined
  }
}

/**
 * Convert algebraic inequality to interval notation
 */
function convertAlgebraicToInterval(variable: string, operator: string, value: number): string[] {
  switch (operator) {
    case '>':
      return [`(${value}, ∞)`]
    case '<':
      return [`(-∞, ${value})`]
    case '≥':
      return [`[${value}, ∞)`]
    case '≤':
      return [`(-∞, ${value}]`]
    default:
      return []
  }
}

/**
 * Convert algebraic inequality to set-builder notation
 */
function convertAlgebraicToSetBuilder(variable: string, operator: string, value: number): string[] {
  return [`{${variable} | ${variable} ${operator} ${value}}`]
}

/**
 * Generate interval notation with corresponding algebraic and set-builder forms
 */
function generateIntervalNotation() {
  const value = randomInt(-10, 10)
  const types = ['open-right', 'open-left', 'closed-right', 'closed-left']
  const type = types[randomInt(0, types.length - 1)]

  switch (type) {
    case 'open-right':
      return {
        notation: `(${value}, ∞)`,
        algebraic: `x > ${value}`,
        setBuilder: `{x | x > ${value}}`
      }
    case 'open-left':
      return {
        notation: `(-∞, ${value})`,
        algebraic: `x < ${value}`,
        setBuilder: `{x | x < ${value}}`
      }
    case 'closed-right':
      return {
        notation: `[${value}, ∞)`,
        algebraic: `x ≥ ${value}`,
        setBuilder: `{x | x ≥ ${value}}`
      }
    case 'closed-left':
      return {
        notation: `(-∞, ${value}]`,
        algebraic: `x ≤ ${value}`,
        setBuilder: `{x | x ≤ ${value}}`
      }
    default:
      return {
        notation: `(${value}, ∞)`,
        algebraic: `x > ${value}`,
        setBuilder: `{x | x > ${value}}`
      }
  }
}

/**
 * Generate set-builder notation with corresponding forms
 */
function generateSetBuilderNotation() {
  const value = randomInt(-10, 10)
  const operators = ['>', '<', '≥', '≤']
  const operator = operators[randomInt(0, operators.length - 1)]

  const notation = `{x | x ${operator} ${value}}`
  const algebraic = `x ${operator} ${value}`

  let interval = ''
  switch (operator) {
    case '>':
      interval = `(${value}, ∞)`
      break
    case '<':
      interval = `(-∞, ${value})`
      break
    case '≥':
      interval = `[${value}, ∞)`
      break
    case '≤':
      interval = `(-∞, ${value}]`
      break
  }

  return { notation, algebraic, interval }
}

/**
 * Generate hints for notation conversion
 */
function generateNotationHint(targetNotation: NotationType): string {
  switch (targetNotation) {
    case 'interval':
      return 'Use parentheses ( ) for strict inequalities and brackets [ ] for inclusive inequalities. Use ∞ for infinity.'
    case 'set-builder':
      return 'Use the format {x | condition} where x is the variable and condition describes the constraint.'
    case 'algebraic':
      return 'Write as a simple inequality using <, >, ≤, or ≥ symbols.'
    default:
      return 'Check the target notation format carefully.'
  }
}

/**
 * Generate domain and range problems
 */
export function generateDomainRangeProblem(
  functionType: FunctionType,
  questionType: 'domain' | 'range' | 'both',
  options: ProblemGeneratorOptions = {}
): DomainRangeProblem {
  const id = `domain-range-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  const functionData = generateFunction(functionType)

  return {
    id,
    type: 'domain-range',
    functionType,
    equation: functionData.equation,
    questionType,
    correctDomain: functionData.domain,
    correctRange: functionData.range,
    hint: options.includeHints ? generateDomainRangeHint(functionType, questionType) : undefined
  }
}

/**
 * Generate function equations with their domains and ranges
 */
function generateFunction(type: FunctionType) {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)

  switch (type) {
    case 'linear':
      return {
        equation: `f(x) = ${a}x + ${b}`,
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }

    case 'quadratic':
      const vertex_y = c - (b * b) / (4 * a)
      if (a > 0) {
        return {
          equation: `f(x) = ${a}x² + ${b}x + ${c}`,
          domain: ['(-∞, ∞)'],
          range: [`[${vertex_y}, ∞)`]
        }
      } else {
        return {
          equation: `f(x) = ${a}x² + ${b}x + ${c}`,
          domain: ['(-∞, ∞)'],
          range: [`(-∞, ${vertex_y}]`]
        }
      }

    case 'cubic':
      return {
        equation: `f(x) = ${a}x³ + ${b}x² + ${c}x`,
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }

    case 'absolute':
      const h = randomInt(-3, 3)
      const k = randomInt(-3, 3)
      return {
        equation: `f(x) = ${a}|x - ${h}| + ${k}`,
        domain: ['(-∞, ∞)'],
        range: a > 0 ? [`[${k}, ∞)`] : [`(-∞, ${k}]`]
      }

    case 'square-root':
      const h_sqrt = randomInt(0, 5)
      const k_sqrt = randomInt(-2, 2)
      return {
        equation: `f(x) = ${a}√(x - ${h_sqrt}) + ${k_sqrt}`,
        domain: [`[${h_sqrt}, ∞)`],
        range: a > 0 ? [`[${k_sqrt}, ∞)`] : [`(-∞, ${k_sqrt}]`]
      }

    default:
      return {
        equation: 'f(x) = x',
        domain: ['(-∞, ∞)'],
        range: ['(-∞, ∞)']
      }
  }
}

/**
 * Generate hints for domain and range problems
 */
function generateDomainRangeHint(functionType: FunctionType, questionType: 'domain' | 'range' | 'both'): string {
  const domainHints = {
    linear: 'Linear functions are defined for all real numbers.',
    quadratic: 'Quadratic functions are defined for all real numbers.',
    cubic: 'Cubic functions are defined for all real numbers.',
    absolute: 'Absolute value functions are defined for all real numbers.',
    'square-root': 'Square root functions require the expression under the radical to be non-negative.'
  }

  const rangeHints = {
    linear: 'Linear functions with non-zero slope have range of all real numbers.',
    quadratic: 'Parabolas opening upward have range [vertex_y, ∞), downward have range (-∞, vertex_y].',
    cubic: 'Cubic functions typically have range of all real numbers.',
    absolute: 'Absolute value functions have range starting from the vertex y-coordinate.',
    'square-root': 'Square root functions have range starting from the y-coordinate of the starting point.'
  }

  if (questionType === 'domain') {
    return domainHints[functionType]
  } else if (questionType === 'range') {
    return rangeHints[functionType]
  } else {
    return `Domain: ${domainHints[functionType]} Range: ${rangeHints[functionType]}`
  }
}

/**
 * Generate display-only notation problems for teacher assessment
 */
export function generateDisplayNotationProblem(): DisplayNotationProblem {
  const id = `display-notation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const types: Array<'english' | 'set-builder' | 'interval'> = ['english', 'set-builder', 'interval']
  const type = types[randomInt(0, types.length - 1)]

  // Define statement structures
  const statementTypes = [
    'all-real',
    'greater-than',
    'greater-equal',
    'less-than',
    'less-equal',
    'between-open',
    'between-closed',
    'between-left-closed',
    'between-right-closed'
  ]

  const statementType = statementTypes[randomInt(0, statementTypes.length - 1)]

  let content = ''
  let description = ''

  switch (type) {
    case 'english':
      description = 'English Statement'
      content = generateEnglishStatement(statementType)
      break

    case 'set-builder':
      description = 'Set-Builder Notation'
      content = generateSetBuilderNotation(statementType)
      break

    case 'interval':
      description = 'Interval Notation'
      content = generateIntervalNotation(statementType)
      break
  }

  return {
    id,
    type,
    content,
    description
  }
}

function generateEnglishStatement(statementType: string): string {
  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10) // Ensure b > a for intervals

  switch (statementType) {
    case 'all-real':
      return 'all real numbers'
    case 'greater-than':
      return `all numbers greater than ${a}`
    case 'greater-equal':
      return `all numbers greater than or equal to ${a}`
    case 'less-than':
      return `all numbers less than ${a}`
    case 'less-equal':
      return `all numbers less than or equal to ${a}`
    case 'between-open':
      return `all numbers between ${a} and ${b}`
    case 'between-closed':
      return `all numbers between ${a} and ${b}, including ${a} and ${b}`
    case 'between-left-closed':
      return `all numbers between ${a} and ${b}, including ${a} but not ${b}`
    case 'between-right-closed':
      return `all numbers between ${a} and ${b}, including ${b} but not ${a}`
    default:
      return 'all real numbers'
  }
}

function generateSetBuilderNotation(statementType: string): string {
  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10)

  switch (statementType) {
    case 'all-real':
      return '{x | x ∈ ℝ}'
    case 'greater-than':
      return `{x | x > ${a}}`
    case 'greater-equal':
      return `{x | x ≥ ${a}}`
    case 'less-than':
      return `{x | x < ${a}}`
    case 'less-equal':
      return `{x | x ≤ ${a}}`
    case 'between-open':
      return `{x | ${a} < x < ${b}}`
    case 'between-closed':
      return `{x | ${a} ≤ x ≤ ${b}}`
    case 'between-left-closed':
      return `{x | ${a} ≤ x < ${b}}`
    case 'between-right-closed':
      return `{x | ${a} < x ≤ ${b}}`
    default:
      return '{x | x ∈ ℝ}'
  }
}

function generateIntervalNotation(statementType: string): string {
  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10)

  switch (statementType) {
    case 'all-real':
      return '(-∞, ∞)'
    case 'greater-than':
      return `(${a}, ∞)`
    case 'greater-equal':
      return `[${a}, ∞)`
    case 'less-than':
      return `(-∞, ${a})`
    case 'less-equal':
      return `(-∞, ${a}]`
    case 'between-open':
      return `(${a}, ${b})`
    case 'between-closed':
      return `[${a}, ${b}]`
    case 'between-left-closed':
      return `[${a}, ${b})`
    case 'between-right-closed':
      return `(${a}, ${b}]`
    default:
      return '(-∞, ∞)'
  }
}

/**
 * Generate display-only domain/range problems for teacher assessment
 */
export function generateDisplayDomainRangeProblem(): DisplayDomainRangeProblem {
  const id = `display-domain-range-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const functionTypes: FunctionType[] = ['linear', 'quadratic', 'cubic', 'absolute', 'square-root']
  const functionType = functionTypes[randomInt(0, functionTypes.length - 1)]

  const questionTypes: Array<'domain' | 'range'> = ['domain', 'range']
  const questionType = questionTypes[randomInt(0, questionTypes.length - 1)]

  // Generate function equation using standard formats
  const equation = generateStandardFunction(functionType)

  return {
    id,
    functionType,
    equation,
    questionType,
    description: `Find the ${questionType.charAt(0).toUpperCase() + questionType.slice(1)}`
  }
}

/**
 * Generate functions in standard mathematical formats
 */
function generateStandardFunction(type: FunctionType): string {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)
  const h = randomInt(-3, 3)
  const k = randomInt(-3, 3)

  switch (type) {
    case 'linear':
      // f(x) = ax + b
      return `f(x) = ${formatCoeff(a)}x${formatConstant(b)}`

    case 'quadratic':
      // f(x) = ax² + bx + c
      return `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`

    case 'cubic':
      // f(x) = ax³ + bx² + cx + d
      const d = randomInt(-5, 5)
      return `f(x) = ${formatCoeff(a)}x³${formatTerm(b, 'x²')}${formatTerm(c, 'x')}${formatConstant(d)}`

    case 'absolute':
      // f(x) = a|x - h| + k
      return `f(x) = ${formatCoeff(a)}|x${formatConstant(-h, true)}|${formatConstant(k)}`

    case 'square-root':
      // f(x) = a√(x - h) + k
      return `f(x) = ${formatCoeff(a)}√(x${formatConstant(-h, true)})${formatConstant(k)}`

    default:
      return `f(x) = x`
  }
}

/**
 * Format coefficient for display
 */
function formatCoeff(coeff: number): string {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

/**
 * Format term with coefficient
 */
function formatTerm(coeff: number, variable: string): string {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

/**
 * Format constant term
 */
function formatConstant(constant: number, forceSign: boolean = false): string {
  if (constant === 0 && !forceSign) return ''
  if (constant > 0) return forceSign ? ` + ${constant}` : ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}