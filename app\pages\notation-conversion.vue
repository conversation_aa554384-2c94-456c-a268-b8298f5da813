<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <UContainer class="py-8">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
          <UButton
            @click="$router.push('/')"
            variant="ghost"
            icon="i-lucide-arrow-left"
            size="lg"
          >
            Back to Home
          </UButton>
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Notation Conversion Game
            </h1>
            <p class="text-gray-600 dark:text-gray-300">
              Convert between different mathematical notations
            </p>
          </div>
        </div>

        <!-- Progress -->
        <div class="text-right">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {{ gameStore.completionPercentage }}%
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ gameStore.state.totalCompleted }} / 37 completed
          </div>
        </div>
      </div>

      <!-- Start Game Button -->
      <div class="text-center mb-8" v-if="!displayMode">
        <UButton
          @click="startDisplayMode"
          size="xl"
          class="px-8 py-4 text-lg"
        >
          <Icon name="i-lucide-play" class="w-6 h-6 mr-2" />
          Start Notation Conversion
        </UButton>
      </div>

      <!-- Progress Bar (only show in grid mode) -->
      <div class="mb-8" v-if="!displayMode">
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Progress</span>
          <span>{{ gameStore.state.totalCompleted }} / 37</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            class="bg-blue-600 h-3 rounded-full transition-all duration-500"
            :style="{ width: `${gameStore.completionPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- Game Grid -->
      <GameGrid v-if="!displayMode" />

      <!-- Display Mode -->
      <NotationDisplayMode
        v-if="displayMode"
        @back-to-grid="displayMode = false"
      />
    </UContainer>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Notation Conversion Game - Math Game Center',
  description: 'Practice converting between algebraic statements, interval notation, and set-builder notation.',
})

// Store
const gameStore = useGameStore()

// State
const displayMode = ref(false)

// Methods
const startDisplayMode = () => {
  displayMode.value = true
}

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('notation-conversion')
})
</script>