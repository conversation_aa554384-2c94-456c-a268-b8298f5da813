<template>
  <div class="max-w-4xl mx-auto">
    <!-- Display Card -->
    <UCard class="mb-6">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
            Notation Conversion Practice
          </h2>
          <UBadge color="blue" variant="soft" size="lg">
            Display Mode
          </UBadge>
        </div>
      </template>

      <div class="space-y-8">
        <!-- Problem Type -->
        <div class="text-center">
          <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
            Problem Type
          </div>
          <UBadge 
            :color="getTypeColor(currentProblem?.type)" 
            variant="soft" 
            size="lg"
            class="text-lg px-4 py-2"
          >
            {{ currentProblem?.description }}
          </UBadge>
        </div>

        <!-- Problem Display -->
        <div class="text-center">
          <div class="p-8 bg-blue-50 dark:bg-blue-900/20 rounded-xl border-2 border-blue-200 dark:border-blue-700">
            <div class="text-blue-900 dark:text-blue-100 font-mono text-3xl font-semibold leading-relaxed">
              {{ currentProblem?.content }}
            </div>
          </div>
        </div>

        <!-- Instructions -->
        <div class="text-center">
          <p class="text-gray-600 dark:text-gray-400 text-lg">
            This problem is displayed for teacher-led assessment and discussion.
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between items-center">
          <UButton 
            @click="$emit('backToGrid')" 
            variant="outline"
            size="lg"
            icon="i-lucide-grid-3x3"
          >
            Back to Grid
          </UButton>
          
          <UButton
            @click="generateNewProblem"
            size="lg"
            icon="i-lucide-refresh-cw"
            :loading="generating"
          >
            Generate Another
          </UButton>
        </div>
      </template>
    </UCard>

    <!-- Problem History (Optional) -->
    <UCard v-if="problemHistory.length > 1">
      <template #header>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Recent Problems
        </h3>
      </template>
      
      <div class="space-y-3">
        <div 
          v-for="(problem, index) in problemHistory.slice(-5).reverse()" 
          :key="problem.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UBadge 
              :color="getTypeColor(problem.type)" 
              variant="soft"
              size="sm"
            >
              {{ problem.description }}
            </UBadge>
            <span class="font-mono text-sm text-gray-700 dark:text-gray-300">
              {{ problem.content }}
            </span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ index === 0 ? 'Current' : `${index + 1} ago` }}
          </span>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { DisplayNotationProblem } from '~/types/game'
import { generateDisplayNotationProblem } from '~/utils/problemGenerator'

interface Emits {
  (e: 'backToGrid'): void
}

defineEmits<Emits>()

// State
const currentProblem = ref<DisplayNotationProblem | null>(null)
const generating = ref(false)
const problemHistory = ref<DisplayNotationProblem[]>([])

// Methods
const generateNewProblem = async () => {
  generating.value = true
  
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const newProblem = generateDisplayNotationProblem()
    
    // Add current problem to history before replacing
    if (currentProblem.value) {
      problemHistory.value.push(currentProblem.value)
    }
    
    currentProblem.value = newProblem
  } finally {
    generating.value = false
  }
}

const getTypeColor = (type?: string) => {
  switch (type) {
    case 'english':
      return 'green'
    case 'set-builder':
      return 'blue'
    case 'interval':
      return 'purple'
    default:
      return 'gray'
  }
}

// Initialize with first problem
onMounted(() => {
  generateNewProblem()
})
</script>
